import { existsSync } from 'node:fs'
import { createRequire } from 'node:module'
import { dirname, join, resolve } from 'node:path'
import process from 'node:process'
import tailwindcss from '@tailwindcss/vite'
import { tanstackRouter } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react-swc'
import { defineConfig, loadEnv, normalizePath } from 'vite'
import { viteStaticCopy } from 'vite-plugin-static-copy'

const require = createRequire(import.meta.url)

const cMapsDir = normalizePath(join(dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps'))
const standardFontsDir = normalizePath(join(dirname(require.resolve('pdfjs-dist/package.json')), 'standard_fonts'))
const wasmDir = normalizePath(join(dirname(require.resolve('pdfjs-dist/package.json')), 'wasm'))

const API_REGEX = /^\/api/

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    optimizeDeps: {
      include: ['@legendapp/state', 'react-pdf', 'pdfjs-dist'],
    },

    plugins: [
      tanstackRouter({
        autoCodeSplitting: true,
        generatedRouteTree: './src/routeTree.gen.ts',
        quoteStyle: 'single',
        routeFileIgnorePrefix: '-',
        routesDirectory: './src/routes',
        target: 'react',
      }),
      react(),
      tailwindcss(),
      viteStaticCopy({
        targets: [
          { src: cMapsDir, dest: '' },
          { src: standardFontsDir, dest: '' },
          { src: wasmDir, dest: '' },
        ],
      }),
    ],

    server: {
      port: 3000,
      cors: true,
      proxy: {
        '/api': {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          secure: true,
          ws: true,
          followRedirects: true,
          autoRewrite: true,

          rewrite: path => path.replace(API_REGEX, ''),

          configure: (proxy, _options) => {
            if (mode === 'production') {
              return
            }

            proxy.on('proxyReq', (proxyReq, req) => {
              console.log(`Request: ${req.method} ${req.url}`)

              proxyReq.setHeader('Cache-Control', 'no-cache')

              proxyReq.removeHeader('If-Modified-Since')
              proxyReq.removeHeader('If-None-Match')

              proxyReq.setHeader('Accept', 'application/json')
            })

            proxy.on('proxyRes', (proxyRes, req) => {
              console.log(`Response: ${proxyRes.statusCode} ${req.url}`)

              if (typeof proxyRes.statusCode === 'number' && proxyRes.statusCode >= 300 && proxyRes.statusCode < 400) {
                console.log(`Redirect location: ${proxyRes.headers.location}`)
              }

              if (proxyRes.statusCode === 304) {
                proxyRes.statusCode = 200
                Reflect.deleteProperty(proxyRes.headers, 'etag')
              }
            })
          },
        },
      },
    },

    publicDir: existsSync(resolve(__dirname, '../../packages/assets'))
      ? resolve(__dirname, '../../packages/assets')
      : resolve('/app/packages/assets'),
  }
})
