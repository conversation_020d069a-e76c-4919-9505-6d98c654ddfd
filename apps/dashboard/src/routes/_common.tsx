import { global$, notificationsApi } from '@mass/api'
import { CommonLayout, DocumentModal, notifications$, TestModal } from '@mass/components/dashboard'
import { i18n } from '@mass/utils'
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'

function RouteComponent() {
  const { t: common } = useTranslation('common')

  return (
    <CommonLayout>
      <DocumentModal
        label={common('user-agreement')}
        suffix={i18n.resolvedLanguage === 'tr' ? "'ni" : ''}
        storeKey='userAgreement'
        apiKey='agreements.user-agreement'
        resultKey='agreement'
      />
      <DocumentModal
        label={common('pdpl')}
        suffix={i18n.resolvedLanguage === 'tr' ? "'yı" : ''}
        storeKey='pdpl'
        apiKey='agreements.kvkk'
        resultKey='kvkk'
      />
      <TestModal />
      <Outlet />
    </CommonLayout>
  )
}

export const Route = createFileRoute('/_common')({
  async beforeLoad() {
    const user = global$.user.get()
    if (!user || user.type === 'anon') {
      throw redirect({ to: '/login', viewTransition: true })
    }

    const count = await notificationsApi.count({
      params: {
        archived: false,
        read: false,
      },
      invalidateCache: true,
    })

    notifications$.count.set(count.count)
  },

  component: RouteComponent,
})
