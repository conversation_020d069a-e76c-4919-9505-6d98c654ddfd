import { use$ } from '@legendapp/state/react'
import { global$, subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { Button, Select, Title } from '@mass/components/shared'
import { i18n, utcDate } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

function SubscriptionOutages() {
  const { t: common } = useTranslation('common')

  // biome-ignore lint/style/noNonNullAssertion: Redundant
  const startYearOptions = use$(() => subscription$.query.startYearOptions.get()!)
  const [fileType, setFileType] =
    useState<Api.ExtractParams<Api.Services['subscriptions']['outages']['export']>['fileType']>('pdf')
  const [year, setYear] = useState(utcDate().startOf('year').toDate())
  const [isLoading, setIsLoading] = useState(false)

  const handleExport = async () => {
    try {
      setIsLoading(true)

      const startDate = utcDate(year).startOf('year').toISOString()
      const endDate = utcDate(year).add(1, 'year').startOf('year').toISOString()

      await toast.promise(
        async () => {
          const { files } = await subscriptionsApi.outages.export({
            query: {
              // biome-ignore lint/style/noNonNullAssertion: Redundant
              id: subscription$.selectedSubscription.get()!.id,
            },

            params: {
              startDate,
              endDate,
              fileType,
              language: global$.language.get() ?? (i18n.language as 'tr' | 'en'),
            },
          })

          for (const file of files) {
            const link = document.createElement('a')
            link.href = URL.createObjectURL(await (await fetch(file.url)).blob())
            link.download = file.fileName
            link.setAttribute('target', '_blank')
            link.click()
            link.remove()
          }
        },
        {
          loading: common('wait'),
          success: common('exported'),
          error: common('something-went-wrong'),
        },
      )
    } catch (err) {
      console.log(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-16 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('file-type')}
        </Title>

        <Select
          value={fileType}
          options={[
            { value: 'pdf', label: 'PDF' },
            { value: 'csv', label: 'CSV' },
            { value: 'xlsx', label: 'XLSX' },
          ]}
          onValueChange={setFileType}
          className='max-w-[350px]'
          disabled={isLoading}
        />
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('year')}
        </Title>

        <Select
          value={year.toISOString()}
          options={startYearOptions.map(date => {
            return {
              value: date.toISOString(),
              label: date.getFullYear().toString(),
            }
          })}
          onValueChange={value => {
            setYear(new Date(value))
          }}
          className='max-w-[350px]'
          disabled={isLoading}
        />
      </div>

      <div
        className={clsx(
          'flex flex-col sm:flex-row sm:items-center sm:justify-end', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <Button variant='primary' className='rounded-c1 sm:w-max' onClick={handleExport} disabled={isLoading}>
          {common('export')}
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/outages')({
  component: SubscriptionOutages,
})
