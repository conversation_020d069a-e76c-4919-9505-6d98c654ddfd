/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */

import { use$ } from '@legendapp/state/react'
import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { Button, Input, Select, Text, Title } from '@mass/components/shared'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useId, useMemo, useState } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

function SubscriptionNotifications() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const userDefinedLimitId = useId()

  const maxThresholdOptions = useMemo(() => {
    return Array.from({
      length: 20,
    }).map((_, index) => ({
      value: `${(100 + (index + 1) * 5) / 100}`, // 1 to 2
      label: `${(index + 1) * 5}%`,
    }))
  }, [])

  const userDefinedLimitBase = use$(() => subscription$.selectedSubscription.userDefinedLimit.get() ?? 0)
  const [userDefinedLimit, setUserDefinedLimit] = useState(`${userDefinedLimitBase}`)

  const unexpectedUsageThresholdBase = use$(
    () => subscription$.selectedSubscription.unexpectedUsageThreshold.get() ?? 0,
  )
  const [unexpectedUsageThreshold, setUnexpectedUsageThreshold] = useState(`${unexpectedUsageThresholdBase}`)

  const [isLoading, setIsLoading] = useState(false)

  const handleApply = async () => {
    try {
      setIsLoading(true)

      await toast.promise(
        async () => {
          await subscriptionsApi.notifications.update({
            query: {
              // biome-ignore lint/style/noNonNullAssertion: Redundant
              id: subscription$.selectedSubscription.get()!.id,
            },

            payload: {
              unexpectedUsageThreshold: Number(unexpectedUsageThreshold),
              userDefinedLimit: Number(userDefinedLimit),
            },
          })

          const newSelectedSubscription = await subscriptionsApi.detail({
            query: {
              // biome-ignore lint/style/noNonNullAssertion: Redundant
              id: subscription$.selectedSubscription.get()!.id,
            },
          })

          subscription$.selectedSubscription.set(newSelectedSubscription)
        },
        {
          loading: common('wait'),
          success: common('operation-completed'),
          error: common('something-went-wrong'),
        },
      )
    } catch (err) {
      console.log(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-16 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-start', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <div
          className={clsx(
            'flex flex-col gap-6', //flex
          )}>
          <Title el='h2' variant='h5' className='text-nowrap'>
            {common('user-defined-limit')}
          </Title>

          <Text variant='subtitle' className='max-w-[750px]'>
            {dashboard('subscriptions.notification.user-defined-limit-note.0')}
            <span className='my-4 block' />
            {dashboard('subscriptions.notification.user-defined-limit-note.1')}
          </Text>
        </div>

        <div className='relative w-full max-w-[350px]'>
          <Input
            id={userDefinedLimitId}
            type='number'
            value={userDefinedLimit}
            onChange={e => setUserDefinedLimit(e.target.value)}
            max='999'
            min='0'
            onBlur={e => {
              const numberified = Number(e.target.value)

              if (numberified < 0) {
                setUserDefinedLimit('0')
              }

              if (numberified > 999) {
                setUserDefinedLimit('999')
              }
            }}
            disabled={isLoading}
            className='pr-24'
          />

          <Text
            el='label'
            htmlFor={userDefinedLimitId}
            className={clsx(
              '-translate-y-1/2 pointer-events-none absolute top-1/2 right-12 h-8 w-8', // positioning
              'transition-colors duration-200', // animation
              'text-center text-dim-3 ',
            )}>
            kWh
          </Text>
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-start', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <div
          className={clsx(
            'flex flex-col gap-6', //flex
          )}>
          <Title el='h2' variant='h5' className='text-nowrap'>
            {common('unexpected-overconsumption-threshold')}
          </Title>

          <Text variant='subtitle' className='max-w-[750px]'>
            {dashboard('subscriptions.notification.unexpected-overconsumption-threshold-note.0')}
            <span className='my-4 block' />
            {dashboard('subscriptions.notification.unexpected-overconsumption-threshold-note.1')}
          </Text>
        </div>

        <Select
          value={unexpectedUsageThreshold}
          options={maxThresholdOptions}
          onValueChange={setUnexpectedUsageThreshold}
          className='max-w-[350px]'
          disabled={isLoading}
        />
      </div>

      <div
        className={clsx(
          'flex flex-col sm:flex-row sm:items-center sm:justify-end', // flex
          'gap-4 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <Button variant='primary' className='rounded-c1 sm:w-max' onClick={handleApply} disabled={isLoading}>
          {common('apply')}
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/notifications')({
  component: SubscriptionNotifications,
})
