import { notificationsApi, subscriptionsApi } from '@mass/api'
import {
  NotificationFiltersModal,
  NotificationResults,
  NotificationsLayout,
  notifications$,
  subscription$,
} from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'

function Notifications() {
  return (
    <>
      <NotificationsLayout>
        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-8 p-8 md:p-16', // spacing
          )}>
          <NotificationResults />
        </div>
      </NotificationsLayout>
      <NotificationFiltersModal />
    </>
  )
}

export const Route = createFileRoute('/_common/notifications')({
  async beforeLoad(ctx) {
    if (ctx.cause === 'stay') {
      return
    }

    const subscriptions = await subscriptionsApi.list({
      invalidateCache: true,
    })

    const categories = await notificationsApi.categories()

    const normalizedCategories = Object.entries(categories.value).reduce(
      (acc, [key, value]) => {
        if (value.subcategories) {
          for (const [subKey, subValue] of Object.entries(value.subcategories)) {
            acc.push({
              type: key,
              subtype: subKey,
              label: {
                tr: subValue.label.TR,
                en: subValue.label.EN,
              },
            })
          }
        } else {
          acc.push({
            type: key,
            label: {
              tr: value.label.TR,
              en: value.label.EN,
            },
          })
        }

        return acc
      },
      [] as Dashboard.Notifications['categories'],
    )

    // TODO: pagination state
    const { content: notifications } = await notificationsApi.list({
      invalidateCache: true,

      params: {
        pageSize: '100',
        'filter:eq': [
          {
            status: 'UNREAD',
          },
          {
            status: 'READ',
          },
        ],
        orderBy: 'createdAt:desc',
      },
    })

    subscription$.subscriptions.set(subscriptions)
    notifications$.categories.set(normalizedCategories)
    notifications$.notifications.set(notifications)
  },

  component: Notifications,
})
