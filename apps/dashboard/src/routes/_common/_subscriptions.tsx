import { subscriptionsApi } from '@mass/api'
import {
  NewSubscriptionModal,
  SubscriptionFiltersModal,
  SubscriptionLayout,
  SubscriptionQueryExportModal,
  subscription$,
  UpdateSubscriptionModal,
} from '@mass/components/dashboard'
import { createFileRoute, Outlet } from '@tanstack/react-router'

function RouteComponent() {
  return (
    <SubscriptionLayout>
      <SubscriptionFiltersModal />
      <NewSubscriptionModal />
      <UpdateSubscriptionModal />
      <SubscriptionQueryExportModal />
      <Outlet />
    </SubscriptionLayout>
  )
}

export const Route = createFileRoute('/_common/_subscriptions')({
  async beforeLoad(ctx) {
    if (ctx.cause === 'stay') {
      return
    }

    const regions = await subscriptionsApi.regions()
    const subscriptions = await subscriptionsApi.list({
      invalidateCache: true,
    })

    const usageLimits = await subscriptionsApi.usage.getLimits()

    subscription$.regions.set(regions)
    subscription$.subscriptions.set(subscriptions)

    subscription$.usageLimits.set({
      max: {
        year: +usageLimits.value.yearMax,
        month: +usageLimits.value.monthMax,
        day: +usageLimits.value.dayMax,
      },
      prev: {
        day: +usageLimits.value.dayPrev,
        month: +usageLimits.value.monthPrev,
      },
    })
  },

  component: RouteComponent,
})
