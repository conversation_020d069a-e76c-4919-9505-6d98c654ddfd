import { Button, Switch, Text, Title } from '@mass/components/shared'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

function NotificationSettings() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')
  const [basicSwitch, setBasicSwitch] = useState(false)

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          'border-accessory-2 border-b', // border
          // 'max-w-[1200px]',
        )}>
        <div className='flex w-full flex-col gap-4 sm:max-w-[600px]'>
          <Title el='h2' variant='h5' className='text-nowrap'>
            {dashboard('settings.outage-notifications')}
          </Title>
          <Text variant='dim-2'> {dashboard('settings.outage-notifications-description')} </Text>
        </div>
        <div className={clsx('flex flex-col items-start', 'ml-auto w-full gap-4 sm:max-w-[240px]')}>
          <Switch
            checked={basicSwitch}
            onChange={setBasicSwitch}
            label={dashboard('settings.planned-outage-notifications')}
          />

          <Switch
            checked={basicSwitch}
            onChange={setBasicSwitch}
            label={dashboard('settings.unplanned-outage-notifications')}
          />
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          // 'max-w-[1200px]',
        )}>
        <div className='flex w-full flex-col gap-4 sm:max-w-[600px]'>
          <Title el='h2' variant='h5' className='text-nowrap'>
            {common('warnings')}
          </Title>
          <Text variant='dim-2'> {dashboard('settings.warnings-description')} </Text>
        </div>
        <div className={clsx('flex flex-col items-start', 'ml-auto w-full gap-4 sm:max-w-[240px]')}>
          <Switch
            checked={basicSwitch}
            onChange={setBasicSwitch}
            label={dashboard('settings.unexpected-usage-notifications')}
          />

          <Switch
            checked={basicSwitch}
            onChange={setBasicSwitch}
            label={dashboard('settings.user-limit-notifications')}
          />
        </div>
      </div>

      <div className='flex w-full sm:justify-end'>
        <Button variant='primary' className='w-full rounded-c1 sm:w-max'>
          {common('save')}
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/settings/notifications')({
  component: NotificationSettings,
})
