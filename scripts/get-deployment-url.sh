C_DEFAULT_BRANCH=$(git rev-parse --abbrev-ref origin/HEAD | sed s~origin/~~)
C_CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

DEFAULT_BRANCH="${CI_DEFAULT_BRANCH:-$C_DEFAULT_BRANCH}"
CURRENT_BRANCH="${CI_COMMIT_BRANCH:-$C_CURRENT_BRANCH}"

BRANCH_PREFIX="branch-"
DOMAIN="dashboard.massportal.com.tr"
BRANCH_SHA=$(echo "$CURRENT_BRANCH" | sha1sum | tr -dc a-z0-9 | head -c 16)

if [ "$CURRENT_BRANCH" == "$DEFAULT_BRANCH" ]; then
    echo "http://$DOMAIN"
else
    echo "http://$BRANCH_PREFIX$BRANCH_SHA.$DOMAIN"
fi
