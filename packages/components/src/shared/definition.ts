import type { PlainObject } from '@legendapp/state'

declare global {
  namespace Shared {
    type Modals = `dashboard.${Dashboard.Modals}` | 'confirmation' | 'regular'

    interface UIStore {
      modal: false | Shared.Modals[]

      confirmation: {
        title: string
        description: string
        onConfirm: PlainObject<() => Promise<void>>
        onCancel: PlainObject<() => Promise<void>>
        toast?: boolean
      } | null

      regular: {
        title: string
        description: string
        onConfirm: PlainObject<() => Promise<void>>
        onCancel: PlainObject<() => Promise<void>>
        toast?: boolean
      } | null

      isModalOpen: PlainObject<(modal: Shared.Modals) => boolean>
      onChangeModal: PlainObject<(modal: Shared.Modals, open: boolean) => void>
      onCloseAllModals: PlainObject<() => void>
    }
  }
}
