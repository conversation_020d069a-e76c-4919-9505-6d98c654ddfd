import { use$ } from '@legendapp/state/react'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import { Button } from '../common/button'
import { <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogOverlay, DialogPortal, DialogRoot } from '../common/dialog'
import { Text } from '../common/text'
import { ui$ } from '../stores/ui'

export const RegularModal: FC = memo(() => {
  const { t: common } = useTranslation('common')

  const regular = use$(() => ui$.regular.get())

  const handleCancel = async () => {
    ui$.regular.set(null)
    ui$.onChangeModal('regular', false)

    if (regular?.onCancel) {
      await (regular.onCancel as () => Promise<void>)()
    }

    if (regular?.toast !== false) {
      toast.success(common('operation-canceled'))
    }
  }

  const handleConfirm = async () => {
    try {
      if (regular?.onConfirm) {
        await (regular.onConfirm as () => Promise<void>)()
      }

      if (regular?.toast !== false) {
        toast.success(common('operation-completed'))
      }

      ui$.onChangeModal('regular', false)
      ui$.regular.set(null)
    } catch (err) {
      console.log(err)
      if (regular?.toast !== false) {
        toast.error(common('something-went-wrong'))
      }
    }
  }

  return (
    <DialogRoot name='regular' onClose={handleCancel}>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-max! sm:max-w-[400px]'
          header={<DialogHeader title={regular?.title ?? ''} />}
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleCancel}>
                {common('close')}
              </Button>
            </DialogFooter>
          }>
          <Text variant='dim-2' className='text-sm'>
            {regular?.description ?? ''}
          </Text>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
