import clsx from 'clsx'
import type { FC } from 'react'

export interface InputStylesProps {
  variant?: 'bordered'
  className?: string | undefined
  error?: boolean
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useInputStyles = ({ className, variant, error }: InputStylesProps) =>
  clsx(
    'flex items-center justify-center',
    'rounded-b1', // styling
    'outline-primary',
    'text-black text-xs', // text
    'transition duration-200', // animation
    'disabled:cursor-not-allowed disabled:opacity-30',
    'w-full px-8 py-5', // sizing
    'bg-white', // background
    'placeholder:text-dim-3', // placeholder styling
    {
      'border border-accessory-1 hover:bg-black/5 focus:border-primary': variant === 'bordered',
      'border border-error': error,
    },
    className,
  )

export const Input: FC<
  React.InputHTMLAttributes<HTMLInputElement> &
    InputStylesProps & {
      ref?: React.Ref<HTMLInputElement>
    }
> = ({ className, variant = 'bordered', error = false, ref, ...props }) => {
  const styles = useInputStyles({ variant, className, error })

  return <input ref={ref} className={styles} {...props} />
}
