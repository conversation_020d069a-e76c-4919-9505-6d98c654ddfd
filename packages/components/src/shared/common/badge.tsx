import clsx from 'clsx'
import type { FC, HTMLAttributes } from 'react'
import { Text } from './text'

export const Badge: FC<
  HTMLAttributes<HTMLSpanElement> & {
    slim?: boolean
    withDot?: boolean
    className?: string
    mode?: 'secondary' | 'success'
    children: React.ReactNode
  }
> = ({ slim = false, withDot = false, mode = 'secondary', className, children, ...props }) => {
  return (
    <span
      className={clsx(
        'flex items-center gap-4', // flex
        {
          'px-4 py-1': slim,
          'px-6 py-2': !slim,
        },
        'rounded-max border', // border
        {
          'border-success-light bg-success-lighter': mode === 'success',
          'border-secondary-light bg-secondary-lighter': mode === 'secondary',
        },
        'text-nowrap',
        className,
      )}
      {...props}>
      {withDot && (
        <span
          className={clsx('h-3 w-3 rounded-full ', {
            'bg-secondary': mode === 'secondary',
            'bg-success': mode === 'success',
          })}
        />
      )}

      <Text
        variant={
          mode === 'secondary' ? (slim ? 'secondary-2xs' : 'secondary-medium') : slim ? 'success-2xs' : 'success-medium'
        }>
        {children}
      </Text>
    </span>
  )
}
