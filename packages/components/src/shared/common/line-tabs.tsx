import { Link, type LinkComponentProps, useLocation, useMatches } from '@tanstack/react-router'
import clsx from 'clsx'
import { type FC, memo } from 'react'

export interface LineTabItem {
  id: string
  label: string
  to: LinkComponentProps['to']
  params?: LinkComponentProps['params']
}

export const LineTabs: FC<{
  tabs: LineTabItem[]
  className?: string
}> = memo(({ tabs, className }) => {
  const matches = useMatches()
  const { pathname } = useLocation()

  // biome-ignore lint/style/noNonNullAssertion: Redundant
  const matchedRoute = tabs.find(tab => tab.to === matches.at(-1)!.fullPath && !!pathname)

  console.log(matches)

  return (
    <nav
      className={clsx(
        'relative max-w-[calc(100vw-2rem)] md:max-w-[calc(100vw-4rem)]', // positioning
        'flex items-center', // flex
        'scrollbar-invisible overflow-x-auto overflow-y-hidden', // scroll
        className,
      )}>
      {tabs.map(tab => (
        <Link
          key={tab.id}
          to={tab.to}
          params={tab.params}
          viewTransition
          className={clsx(
            'relative', // positioning
            'flex items-center', // flex
            'px-6 py-3', // spacing
            'text-nowrap font-medium text-dim-2 text-xs', // typography
            'transition-colors duration-200', // animation
            {
              'text-primary': tab.id === matchedRoute?.id,
            },
          )}>
          {tab.label}

          <span
            className={clsx(
              'absolute right-0 bottom-0 left-0', // positioning
              'h-1 w-full', // sizing
              'rounded-t-max', // styling
              'transition-colors duration-200', // animation
              {
                'bg-primary': tab.id === matchedRoute?.id,
              },
            )}
          />
        </Link>
      ))}
    </nav>
  )
})
