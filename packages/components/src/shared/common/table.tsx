/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */

import type { Observable } from '@legendapp/state'
import { use$ } from '@legendapp/state/react'
import { ChevronDownIcon, SkipBackIcon, SkipForwardIcon } from '@mass/icons'
import type { BlobType, Paths } from '@mass/utils'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type Header,
  type PaginationState,
  type RowData,
  type SortingFn,
  type SortingState,
  useReactTable,
} from '@tanstack/react-table'
import clsx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button } from './button'
import { Popover } from './popover'
import { Select } from './select'
import { Text } from './text'

const DATA = [] as BlobType[]

declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends RowData, TValue> {
    sortable?: SortingFn<TData>
  }
}

export interface TableColumnOptions<T> {
  key: Exclude<Paths<T>, number>
  label: string
  additionalLabel?: string
  description?: string

  sortable?: SortingFn<T>
  render?: (value: BlobType, row: T) => React.ReactNode
}

export interface TableOptions<T> {
  data: Observable<T>[]
  columns: TableColumnOptions<NoInfer<T>>[]

  selectable?: (row: T) => void
  className?: string
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redunant
export const columnHelper = createColumnHelper<BlobType>()

export const TableHeader: FC<{
  header: Header<BlobType, unknown>
}> = ({ header }) => {
  const { t: common } = useTranslation('common')

  const canSort = useMemo(() => {
    return header.column.getCanSort()
  }, [header])

  return (
    <th className={clsx('text-nowrap px-8 py-4')}>
      <div className='flex w-full items-center justify-between'>
        <div className='flex items-center gap-4'>
          {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}

          {canSort && (
            <ChevronDownIcon
              className={clsx(
                'h-8 w-8 text-dim-3', // sizing
                'transition duration-300', // animation
                {
                  'opacity-0': header.column.getIsSorted() === false,
                  'opacity-100': header.column.getIsSorted() === 'asc',
                  'rotate-180 opacity-100': header.column.getIsSorted() === 'desc',
                },
              )}
            />
          )}
        </div>

        {canSort && (
          <Select
            unstyled
            placeholder=''
            className='items-center justify-center'
            iconClassName='h-8! w-8!'
            hideValue
            options={[
              { value: 'none', label: common('default') },
              { value: 'asc', label: common('a-z') },
              { value: 'desc', label: common('z-a') },
            ]}
            value={header.column.getIsSorted() || 'none'}
            onValueChange={value => {
              header.column.toggleSorting(value === 'asc' ? false : value === 'desc' ? true : undefined)
            }}
          />
        )}
      </div>
    </th>
  )
}
// TODO: Empty State
export const Table = <T,>({ data: $observableData, columns: rawColumns, className, selectable }: TableOptions<T>) => {
  const { t: common } = useTranslation('common')

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })
  const [sorting, setSorting] = useState<SortingState>([])

  const columns = useMemo(() => {
    return rawColumns.map(rawColumn => {
      return columnHelper.accessor(rawColumn.key as BlobType, {
        id: rawColumn.key,
        header: () => (
          <div className='flex flex-row items-center gap-4'>
            {rawColumn.description && (
              <Popover
                variant='icon-slim-bordered'
                popoverPosition='bottom'
                popoverUnstyled
                buttonContent={() => <Text className='h-5 w-5 font-medium text-[10px]/5'> i </Text>}>
                <Text variant='dim-2' className='rounded-c1 border border-accessory-1 px-6 py-3 text-2xs!'>
                  {rawColumn.description}
                </Text>
              </Popover>
            )}

            <div className='flex items-center gap-4'>
              <Text variant='dimmer'>{rawColumn.label}</Text>

              {rawColumn.additionalLabel && (
                <Text variant='dim-3-3xs' className='normal-case'>
                  {rawColumn.additionalLabel}
                </Text>
              )}
            </div>
          </div>
        ),

        cell: info => {
          if (rawColumn.render) {
            return rawColumn.render(info.getValue(), info.row.original)
          }

          return info.getValue()
        },

        enableSorting: !!rawColumn.sortable,
        sortUndefined: false,
        ...(rawColumn.sortable ? { sortingFn: rawColumn.sortable } : {}),
      })
    })
  }, [rawColumns])

  const data = use$($observableData)

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(), //client-side sorting
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onPaginationChange: setPagination,

    state: {
      pagination,
      sorting,
    },
  })

  return (
    <div
      className={clsx(
        'relative shadow-layer-2', // positioning
        'rounded-b1 border border-accessory-1', // border
        'overflow-x-auto', // scroll
        className,
      )}>
      <table
        className={clsx(
          'w-full ', // sizing
          'text-left text-dim-2 text-xs', // text
          'border-collapse', // table
        )}>
        <thead>
          {table.getHeaderGroups().map(headerGroup => (
            <tr
              key={headerGroup.id}
              className={clsx(
                'uppercase', // text
                'border-accessory-1 border-b', // border
              )}>
              {headerGroup.headers.map(header => (
                <TableHeader key={header.id} header={header} />
              ))}
            </tr>
          ))}
        </thead>

        <tbody
          className={clsx(
            '*:border-accessory-1 *:border-b *:last:border-0', // border
            '*:nth-[2n]:bg-whity/60', // zebra
            '*:hover:bg-black/5 *:nth-[2n]:hover:bg-whity', // hover
            'transition duration-300', // animation
          )}>
          {table.getRowModel().rows.map(row => (
            <tr
              key={row.id}
              className={clsx({
                'cursor-pointer': selectable,
              })}
              onClick={() => {
                if (selectable) {
                  selectable(row.original)
                }
              }}
              onKeyDown={e => {
                if (e.key === 'Enter' && selectable) {
                  selectable(row.original)
                }
              }}
              {...(selectable ? { tabIndex: 0 } : {})}>
              {row.getVisibleCells().map(cell => (
                <td key={cell.id} className={clsx('text-nowrap px-8 py-3')}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      <div
        className={clsx(
          'border-accessory-1 border-t', // border
          'flex flex-col items-center justify-between sm:flex-row', // flex
          'gap-8 px-8 py-3', // spacing
        )}>
        <Text variant='dimmer'>
          {common('total-x-entries', { count: table.getPrePaginationRowModel().rows.length })}
        </Text>

        <div
          className={clsx(
            'flex flex-row items-center gap-4', // flex
          )}>
          <Button variant='icon-slim-bordered' onClick={() => table.firstPage()} disabled={!table.getCanPreviousPage()}>
            <SkipBackIcon className='h-8 w-8' />
          </Button>
          <Button
            variant='icon-slim-bordered'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}>
            <ChevronDownIcon className='h-8 w-8 rotate-90' />
          </Button>

          <Text variant='dimmer'>
            {common('page-x-of-y', {
              current: table.getPageCount() === 0 ? 0 : table.getState().pagination.pageIndex + 1,
              total: table.getPageCount(),
            })}
          </Text>

          <Button variant='icon-slim-bordered' onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
            <ChevronDownIcon className='-rotate-90 h-8 w-8' />
          </Button>
          <Button variant='icon-slim-bordered' onClick={() => table.lastPage()} disabled={!table.getCanNextPage()}>
            <SkipForwardIcon className='h-8 w-8' />
          </Button>
        </div>
      </div>
    </div>
  )
}
