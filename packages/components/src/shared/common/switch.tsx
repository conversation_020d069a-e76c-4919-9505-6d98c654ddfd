import { Switch as $Switch } from '@headlessui/react'
import clsx from 'clsx'
import { type FC, useId } from 'react'
import { Text } from './text'

interface SwitchProps extends SwitchStylesProps {
  label?: string
  checked?: boolean
  onChange?: (checked: boolean) => void
  id?: string
  name?: string
  value?: string
}

const useSwitchStyles = ({ className, error, disabled }: SwitchStylesProps) =>
  clsx(
    'group relative inline-flex h-10 w-18 items-center rounded-max', // sizing and shape
    'transition-colors duration-200 ease-in-out', // animation
    'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2', // focus states
    'bg-accessory-1', // default background (unchecked state)
    'data-checked:bg-primary', // checked state background
    {
      'cursor-not-allowed opacity-50': disabled,
      'cursor-pointer': !disabled,
      'data-checked:bg-error': !disabled && error, // error state when checked
      'bg-dim-3': disabled, // disabled background
    },
    className,
  )

const useSwitchThumbStyles = ({ disabled }: Pick<SwitchStylesProps, 'disabled'>) =>
  clsx(
    'inline-block h-8 w-8 transform rounded-max', // sizing and shape
    'transition-transform duration-200 ease-in-out', // animation
    'translate-x-1 group-data-checked:translate-x-9', // position states using group
    {
      'bg-white': !disabled,
      'bg-dim-3': disabled,
      'shadow-sm': !disabled,
    },
  )

export interface SwitchStylesProps {
  className?: string | undefined
  error?: boolean
  disabled?: boolean
  label?: string
  checked?: boolean
  onChange?: (checked: boolean) => void
  name?: string
  value?: string
}

export const Switch: FC<SwitchProps & { ref?: React.Ref<HTMLButtonElement> | undefined }> = ({
  ref,
  className,
  error = false,
  disabled = false,
  label,
  checked = false,
  onChange,
  name,
  value,
  ...props
}) => {
  const switchStyles = useSwitchStyles({ className, error, disabled })
  const thumbStyles = useSwitchThumbStyles({ disabled })
  const id = useId()

  const switchProps = {
    id,
    checked,
    disabled,
    className: switchStyles,
    ...(name && { name }),
    ...(value && { value }),
    ...(onChange && { onChange }),
    ...props,
  }

  return (
    <div className='flex items-center gap-3'>
      <$Switch {...(ref ? { ref } : {})} {...switchProps}>
        <span className={thumbStyles} />
      </$Switch>

      {label && (
        <Text
          el='label'
          variant='dim-2'
          htmlFor={id}
          className={clsx('cursor-pointer font-medium text-xs', {
            'text-dim-3': disabled,
            'text-error': error,
          })}>
          {label}
        </Text>
      )}
    </div>
  )
}
