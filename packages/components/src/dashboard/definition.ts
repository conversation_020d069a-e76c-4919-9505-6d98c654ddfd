import type { ArrayValue, PlainObject } from '@legendapp/state'
import type { Type } from 'arktype'

declare global {
  namespace Dashboard {
    type Modals =
      | 'subscription-filters'
      | 'new-subscription'
      | 'update-subscription'
      | 'export-subscription-query'
      | 'notifications-filters'
      | `document-${keyof Api.Stores.Global['aggreements']}`
      | 'test'

    type SubscriptionParams = Api.ExtractParams<Api.Services['subscriptions']['list']>

    interface SubscriptionFilters {
      params: SubscriptionParams

      clearFilters: () => void

      eq: PlainObject<
        <
          T extends keyof ArrayValue<SubscriptionParams['filter:eq']>,
          const D,
          R = ArrayValue<SubscriptionParams['filter:eq']>[T] | D,
        >(
          type: T,
          options: {
            defaultValue: D
            removeIfDefault?: boolean
          },
        ) => {
          value: Exclude<R | D, undefined>
          set: (value: R) => void
        }
      >
    }

    type NewSubscriptionPayload = Api.ExtractPayload<Api.Services['subscriptions']['create']>

    interface NewSubscription {
      payload: Partial<NewSubscriptionPayload>
      errors: Record<keyof NewSubscriptionPayload, boolean>
      dirty: Record<keyof NewSubscriptionPayload, boolean>

      clearPayload: PlainObject<() => void>

      use: PlainObject<
        <T extends keyof NewSubscriptionPayload>(
          type: T,
          checker?: Type<NewSubscriptionPayload[T]>,
        ) => {
          value: NewSubscriptionPayload[T]
          error: boolean
          set: (value: NewSubscriptionPayload[T]) => void
          setDirty: () => void
        }
      >
    }

    type UpdateSubscriptionPayload = Api.ExtractPayload<Api.Services['subscriptions']['update']>

    interface UpdateSubscription {
      original: ArrayValue<Api.ExtractResponse<Api.Services['subscriptions']['list']>['content']> | null
      payload: Partial<UpdateSubscriptionPayload>
      errors: Record<keyof UpdateSubscriptionPayload, boolean>
      dirty: Record<keyof UpdateSubscriptionPayload, boolean>

      clearPayload: PlainObject<() => void>

      use: PlainObject<
        <T extends keyof UpdateSubscriptionPayload>(
          type: T,
          checker?: Type<UpdateSubscriptionPayload[T]>,
        ) => {
          value: UpdateSubscriptionPayload[T]
          error: boolean
          set: (value: UpdateSubscriptionPayload[T]) => void
          setDirty: () => void
        }
      >
    }

    type Periods = 'yearly' | 'monthly' | 'daily'
    type PopulatedUsages = {
      name: Date | string

      t0: number
      t1: number
      t2: number
      t3: number

      p0?: number
    }

    interface Subscription {
      regions: Api.ExtractResponse<Api.Services['subscriptions']['regions']> | null
      subscriptions: Api.ExtractResponse<Api.Services['subscriptions']['list']> | null

      selectedSubscription: ArrayValue<Api.ExtractResponse<Api.Services['subscriptions']['list']>['content']> | null

      usageLimits: {
        max: {
          year: number
          month: number
          day: number
        }
        prev: {
          day: number
          month: number
        }
      } | null

      query: {
        startYearOptions: Date[]

        startMonthOptions: Date[]
        endMonthOptions: Date[]

        values: {
          period: Dashboard.Periods
          isRange: boolean
          isLastX: boolean
          selectedDate: (Date | null)[]
        }

        populated: Api.ExtractParams<Api.Services['subscriptions']['usage']['data']> | null

        errors: Record<keyof Dashboard.Subscription['query']['values'], boolean>
        dirty: Record<keyof Dashboard.Subscription['query']['values'], boolean>

        setPeriod: PlainObject<(period: Dashboard.Periods) => void>
        setIsRange: PlainObject<(isRange: boolean) => void>
        setIsLastX: PlainObject<(isLastX: boolean) => void>
        setSelectedDate: PlainObject<(selectedDate: (Date | null)[]) => void>

        setDirty: PlainObject<(key: keyof Dashboard.Subscription['query']['values']) => void>

        checkAll: PlainObject<() => void>

        clear: PlainObject<() => void>
      }

      usage: {
        data: PlainObject<Api.ExtractResponse<Api.Services['subscriptions']['usage']['data']>> | null
        isLoading: boolean

        populated: Dashboard.PopulatedUsages[]
        populatedAverages: Dashboard.PopulatedUsages[]

        mode: 'table' | 'chart' | 'hourly'
        timeMode: 'monochronic' | 'polychronic'

        clear: PlainObject<() => void>
      }
    }

    type NotificationStatuses = 'active' | 'archived'

    interface Notifications {
      count: number
      notifications: Api.ExtractResponse<Api.Services['notifications']['list']>['content']

      categories: {
        type: string
        subtype?: string
        label: {
          tr: string
          en: string
        }
      }[]

      query: {
        values: {
          status: Dashboard.NotificationStatuses
          subscriptionId: string
          notificationType: string
        }

        errors: Record<keyof Notifications['query']['values'], boolean>
        dirty: Record<keyof Notifications['query']['values'], boolean>

        setDirty: PlainObject<(key: keyof Notifications['query']['values']) => void>

        setStatus: PlainObject<(status: Dashboard.NotificationStatuses) => void>
        setSubscriptionId: PlainObject<(subscriptionId: string) => void>
        setNotificationType: PlainObject<(notificationType: string) => void>

        checkAll: PlainObject<() => void>
        clear: PlainObject<() => void>
        filter: PlainObject<() => Promise<void>>
      }
    }
  }
}
