import { use$ } from '@legendapp/state/react'
import clsx from 'clsx'
import { type FC, memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Select, Text, Title, ui$ } from '../../../shared'
import { useMeta } from '../../hooks/use-meta'
import { notifications$ } from '../../stores/notifications'
import { Breadcrumbs } from '../shared/breadcrumbs'

const NotificationsHeaderActions: FC = memo(() => {
  const { t: common } = useTranslation('common')

  const notificationQueryStatus = use$(() => notifications$.query.values.status.get())

  return (
    <div className='flex gap-4'>
      <Select
        value={notificationQueryStatus}
        options={[
          { value: 'active', label: common('active') },
          { value: 'archived', label: common('archived') },
        ]}
        className='max-w-[350px] rounded-c1'
        onValueChange={value => {
          notifications$.query.setStatus(value)

          notifications$.query.filter()
        }}
      />

      <Button
        variant='bordered'
        className='rounded-c1'
        onClick={() => ui$.onChangeModal('dashboard.notifications-filters', true)}>
        <Text variant='dim-2-medium' className='text-nowrap'>
          {common('filter')}
        </Text>
      </Button>
    </div>
  )
})

export const Header: FC = memo(() => {
  const { title, description, hasTabs } = useMeta()

  const titleEl = useMemo(() => {
    return <Title> {title} </Title>
  }, [title])

  return (
    <header
      className={clsx(
        'flex flex-col gap-16', // flex
        'h-auto w-full max-w-screen', // sizing
        'px-8 pt-8 md:px-16 md:pt-16', // padding
        'border-accessory-1 border-b', // accessory
        {
          'pb-8 md:pb-16': !hasTabs,
        },
      )}>
      <Breadcrumbs />

      <div
        className={clsx(
          'flex flex-col justify-between gap-12 md:flex-row md:items-end md:gap-4', // flex
        )}>
        <div
          className={clsx(
            'flex flex-row', // flex
          )}>
          <div
            className={clsx(
              'flex flex-col gap-4', // flex
            )}>
            <div className='flex items-center gap-8'>{titleEl}</div>
            <Text variant='dim-2'> {description} </Text>
          </div>
        </div>

        <NotificationsHeaderActions />
      </div>
    </header>
  )
})
