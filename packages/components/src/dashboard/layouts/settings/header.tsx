import { useLocation } from '@tanstack/react-router'
import clsx from 'clsx'
import { type FC, memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, type LineTabItem, LineTabs, Text, Title } from '../../../shared'
import { useMeta } from '../../hooks/use-meta'
import { Breadcrumbs } from '../shared/breadcrumbs'

const SettingsTabs: FC = memo(() => {
  const { t: common } = useTranslation('common')

  const tabs: LineTabItem[] = [
    {
      id: 'general',
      label: common('general'),
      to: '/settings/',
    },
    {
      id: 'app-notifications',
      label: common('app-notifications'),
      to: '/settings/notifications',
    },
  ]

  return <LineTabs tabs={tabs} className='mt-8' />
})

const SettingsHeaderActions: FC = memo(() => {
  const { t: common } = useTranslation('common')

  return (
    <div className='flex gap-4 pb-8'>
      <Button variant='primary' className='rounded-c1'>
        <Text variant='white' className='text-nowrap'>
          {common('activate-all')}
        </Text>
      </Button>
    </div>
  )
})

export const Header: FC = memo(() => {
  const { title, description, hasTabs } = useMeta()
  const { pathname } = useLocation()

  const titleEl = useMemo(() => {
    return <Title> {title} </Title>
  }, [title])

  return (
    <header
      className={clsx(
        'flex flex-col gap-16', // flex
        'h-auto w-full max-w-screen', // sizing
        'px-8 pt-8 md:px-16 md:pt-16', // padding
        'border-accessory-1 border-b', // accessory
        {
          'pb-8 md:pb-16': !hasTabs,
        },
      )}>
      <Breadcrumbs />

      <div
        className={clsx(
          'flex flex-col justify-between gap-12 md:flex-row md:items-end md:gap-4', // flex
        )}>
        <div
          className={clsx(
            'flex flex-row', // flex
          )}>
          <div
            className={clsx(
              'flex flex-col gap-4', // flex
            )}>
            <div className='flex items-center gap-8'>{titleEl}</div>
            <Text variant='dim-2'> {description} </Text>

            {hasTabs && <SettingsTabs />}
          </div>
        </div>

        {pathname === '/settings/notifications' && <SettingsHeaderActions />}
      </div>
    </header>
  )
})
