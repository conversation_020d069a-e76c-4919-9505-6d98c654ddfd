import clsx from 'clsx'
import type { <PERSON> } from 'react'

import { Navigation } from './navigation'

export const CommonLayout: FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <div className={clsx('flex max-h-screen')}>
      {/* Nav */}
      <Navigation />

      {/* Common pages */}
      <main
        className={clsx(
          'flex flex-col', // flex
          'h-auto min-h-screen w-full', // sizing
          'overflow-y-auto overflow-x-hidden',
        )}>
        {children}
      </main>
    </div>
  )
}
