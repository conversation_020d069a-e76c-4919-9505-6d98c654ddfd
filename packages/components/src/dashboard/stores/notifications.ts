import { ObservableHint, observable } from '@legendapp/state'
import { notificationsApi } from '@mass/api'
import { type } from 'arktype'
import { ui$ } from '../../shared'

export const notificationQueryValidation = type({
  status: "'active' | 'archived'",
  subscriptionId: 'string',
  notificationType: 'string',
})

export const notifications$ = observable<Dashboard.Notifications>({
  count: 0,
  categories: [],
  notifications: [],

  query: {
    values: {
      status: 'active',
      subscriptionId: 'all',
      notificationType: 'all',
    },

    errors: {
      status: false,
      subscriptionId: false,
      notificationType: false,
    },

    dirty: {
      status: false,
      subscriptionId: false,
      notificationType: false,
    },

    setStatus: ObservableHint.function(status => {
      notifications$.query.values.status.set(status)
      notifications$.query.setDirty('status')
    }),

    setSubscriptionId: ObservableHint.function(subscriptionId => {
      notifications$.query.values.subscriptionId.set(subscriptionId)
      notifications$.query.setDirty('subscriptionId')
    }),

    setNotificationType: ObservableHint.function(notificationType => {
      notifications$.query.values.notificationType.set(notificationType)
      notifications$.query.setDirty('notificationType')
    }),

    setDirty: ObservableHint.function(key => {
      notifications$.query.errors[key].set(
        notificationQueryValidation.get(key)(notifications$.query.values[key].get()) instanceof type.errors,
      )

      if (notificationQueryValidation.get(key)(notifications$.query.values[key].get()) instanceof type.errors) {
        console.log(notificationQueryValidation.get(key)(notifications$.query.values[key].get()))
      }

      notifications$.query.dirty[key].set(true)
    }),

    checkAll: ObservableHint.function(() => {
      notifications$.query.setDirty('status')
      notifications$.query.setDirty('subscriptionId')
      notifications$.query.setDirty('notificationType')

      const out = notificationQueryValidation(notifications$.query.values.get())

      if (out instanceof type.errors) {
        console.log(out)
      }
    }),

    clear: ObservableHint.function(() => {
      notifications$.query.values.set({
        status: 'active',
        subscriptionId: 'all',
        notificationType: 'all',
      })

      notifications$.query.errors.set({
        status: false,
        subscriptionId: false,
        notificationType: false,
      })

      notifications$.query.dirty.set({
        status: false,
        subscriptionId: false,
        notificationType: false,
      })
    }),

    filter: ObservableHint.function(async () => {
      const query = notifications$.query.values.get()

      const filters = [] as Exclude<Api.ExtractParams<Api.Services['notifications']['list']>['filter:eq'], undefined>

      if (query.status === 'active') {
        filters.push({ status: 'UNREAD' })
        filters.push({ status: 'READ' })
      } else if (query.status === 'archived') {
        filters.push({ status: 'ARCHIVED' })
      }

      if (query.subscriptionId !== 'all') {
        filters.push({ subscriptionId: query.subscriptionId })
      }

      if (query.notificationType !== 'all') {
        const [notificationType, notificationSubtype] = query.notificationType.split('-')

        if (notificationType) {
          filters.push({ type: notificationType })
        }

        if (notificationSubtype) {
          filters.push({ subtype: notificationSubtype })
        }
      }

      const { content: notifications } = await notificationsApi.list({
        params: {
          pageSize: '100',

          'filter:eq': filters,
          orderBy: 'createdAt:desc',
        },
        invalidateCache: true,
      })

      const count = await notificationsApi.count({
        params: {
          archived: false,
          read: false,
        },
        invalidateCache: true,
      })

      notifications$.count.set(count.count)

      notifications$.notifications.set(notifications)
      ui$.onChangeModal('dashboard.notifications-filters', false)
    }),
  },
})
