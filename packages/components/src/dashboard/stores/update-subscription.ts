import { ObservableHint, observable } from '@legendapp/state'
import type { BlobType } from '@mass/utils'
import { type } from 'arktype'

export const updateSubscription$ = observable<Dashboard.UpdateSubscription>({
  original: null,
  payload: {},
  errors: {
    name: false,
  },
  dirty: {
    name: false,
  },

  clearPayload: ObservableHint.function(() => {
    updateSubscription$.payload.set({})
  }),

  use: ObservableHint.function((key, checker): BlobType => {
    const set = (value: BlobType) => {
      updateSubscription$.dirty[key].set(true)

      if (checker) {
        updateSubscription$.errors[key].set(checker(value) instanceof type.errors)
      }

      updateSubscription$.payload.set(prev => {
        return {
          ...prev,
          [key]: value,
        }
      })
    }

    return {
      value: updateSubscription$.payload.get()[key],
      error: updateSubscription$.dirty[key].get() && updateSubscription$.errors[key].get(),
      set,
      setDirty: () => {
        set(updateSubscription$.payload.get()[key])
      },
    }
  }),
})
