import { ObservableHint, observable } from '@legendapp/state'
import type { BlobType } from '@mass/utils'

export const subscriptionFilters$ = observable<Dashboard.SubscriptionFilters>({
  params: {},

  clearFilters: ObservableHint.function(() => {
    subscriptionFilters$.params.set({})
  }),

  eq: ObservableHint.function((type, { defaultValue, removeIfDefault = true }): BlobType => {
    const foundValue = subscriptionFilters$.params['filter:eq']
      .get()
      ?.find(filter => typeof filter[type] !== 'undefined')?.[type]

    return {
      value: foundValue ?? defaultValue,
      set: (newValue: BlobType) => {
        const foundIndex = (subscriptionFilters$.params['filter:eq'].get() ?? []).findIndex(
          filter => typeof filter[type] !== 'undefined',
        )

        if (foundIndex > -1) {
          const filtered =
            subscriptionFilters$.params['filter:eq'].get()?.filter((_, index) => index !== foundIndex) ?? []

          subscriptionFilters$.params['filter:eq'].set(filtered)
        }

        if (newValue === defaultValue && removeIfDefault) {
          return
        }

        subscriptionFilters$.params['filter:eq'].push({ [type]: newValue })
      },
    }
  }),
})
