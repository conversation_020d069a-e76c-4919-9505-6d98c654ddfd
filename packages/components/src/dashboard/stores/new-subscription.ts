import { ObservableHint, observable } from '@legendapp/state'
import type { BlobType } from '@mass/utils'
import { type } from 'arktype'

export const newSubscription$ = observable<Dashboard.NewSubscription>({
  payload: {},
  errors: {
    name: false,
    individual: false,
    personIdentifier: false,
    regionId: false,
    installationId: false,
  },
  dirty: {
    name: false,
    individual: false,
    personIdentifier: false,
    regionId: false,
    installationId: false,
  },

  clearPayload: ObservableHint.function(() => {
    newSubscription$.payload.set({})
  }),

  use: ObservableHint.function((key, checker): BlobType => {
    const set = (value: BlobType) => {
      newSubscription$.dirty[key].set(true)

      if (checker) {
        newSubscription$.errors[key].set(checker(value) instanceof type.errors)
      }

      newSubscription$.payload.set(prev => {
        return {
          ...prev,
          [key]: value,
        }
      })
    }

    return {
      value: newSubscription$.payload.get()[key],
      error: newSubscription$.dirty[key].get() && newSubscription$.errors[key].get(),
      set,
      setDirty: () => {
        set(newSubscription$.payload.get()[key])
      },
    }
  }),
})
