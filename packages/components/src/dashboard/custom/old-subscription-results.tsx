// import { use$ } from '@legendapp/state/react'
// import { ChevronDownIcon } from '@mass/icons'
// import { useNumberFormat } from '@mass/utils'
// import clsx from 'clsx'
// import { type FC, memo } from 'react'
// import { useTranslation } from 'react-i18next'
// import { Popover, Text, Title } from '../../shared'
// import { subscription$ } from '../stores/subscription'

// export const SubscriptionCards: FC = memo(() => {
//   const { t: common } = useTranslation('common')
//   const { t: dashboard } = useTranslation('dashboard')

//   const isProduction = use$(() => subscription$.selectedSubscription.get()?.type === 'electricity-production')
//   const timeMode = use$(() => subscription$.usage.timeMode.get())
//   const populatedGranularity = use$(() => subscription$.query.populated.granularity.get())
//   const populatedComparisons = use$(() => subscription$.usage.populatedComparisons.get())
//   const numberFormat = useNumberFormat()

//   return (
//     <div
//       className={clsx(
//         'relative ', // positioning
//         'overflow-x-auto', // scroll
//       )}>
//       <div
//         className={clsx(
//           'w-max min-w-full',
//           'grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3', // grid
//           'gap-x-10 gap-y-4', // spacing
//         )}>
//         <div
//           className={clsx(
//             'relative col-span-1 flex flex-row items-center justify-around lg:max-2xl:col-span-2', // flex
//             'h-full px-10 py-5 md:p-8', // spacing
//             'rounded-c1 border border-accessory-1 shadow-layer-2', // border
//           )}>
//           <Title el='h2' variant='giant'>
//             {common('average')}
//           </Title>

//           <ChevronDownIcon className='-rotate-90 h-9 w-9 text-dim-3 md:h-12 md:w-12 2xl:h-19 2xl:w-19' />

//           <div className='flex items-center gap-4'>
//             <Text variant='giant'> {numberFormat(populatedComparisons.average)} </Text>
//             <Text variant='giant-dimmer'> (kWh) </Text>
//           </div>

//           <Popover
//             variant='icon-slim-bordered'
//             popoverPosition='bottom'
//             popoverUnstyled
//             popoverWidth={250}
//             className='absolute! inset-0'
//             buttonClassName={clsx('absolute top-4 right-4')}
//             buttonContent={() => <Text className='h-5 w-5 font-medium text-[10px]/5'> i </Text>}>
//             <Text variant='dim-2' className='rounded-c1 border border-accessory-1 px-6 py-3 text-2xs!'>
//               {dashboard('subscriptions.query-average-note')}
//             </Text>
//           </Popover>
//         </div>

//         <div
//           className={clsx(
//             'relative col-span-1 flex flex-row items-center justify-around', // flex
//             'h-full px-10 py-5 md:p-8', // spacing
//             'rounded-c1 border border-accessory-1 shadow-layer-2', // border
//           )}>
//           <Title el='h2' variant='giant'>
//             {common('city-average')}
//           </Title>

//           <ChevronDownIcon className='-rotate-90 h-9 w-9 text-dim-3 md:h-12 md:w-12 2xl:h-19 2xl:w-19' />

//           <div className='flex items-center gap-4'>
//             <Text variant='giant'> {numberFormat(populatedComparisons.city)} </Text>
//             <Text variant='giant-dimmer'> (kWh) </Text>
//           </div>

//           <Popover
//             variant='icon-slim-bordered'
//             popoverPosition='bottom'
//             popoverUnstyled
//             popoverWidth={250}
//             className='absolute! inset-0'
//             buttonClassName={clsx('absolute top-4 right-4')}
//             buttonContent={() => <Text className='h-5 w-5 font-medium text-[10px]/5'> i </Text>}>
//             <Text variant='dim-2' className='rounded-c1 border border-accessory-1 px-6 py-3 text-2xs!'>
//               {dashboard('subscriptions.query-city-average-note')}
//             </Text>
//           </Popover>
//         </div>

//         <div
//           className={clsx(
//             'relative col-span-1 flex flex-row items-center justify-around', // flex
//             'h-full px-10 py-5 md:p-8', // spacing
//             'rounded-c1 border border-accessory-1 shadow-layer-2', // border
//           )}>
//           <Title el='h2' variant='giant'>
//             {common('district-average')}
//           </Title>

//           <ChevronDownIcon className='-rotate-90 h-9 w-9 text-dim-3 md:h-12 md:w-12 2xl:h-19 2xl:w-19' />

//           <div className='flex items-center gap-4'>
//             <Text variant='giant'> {numberFormat(populatedComparisons.district)} </Text>
//             <Text variant='giant-dimmer'> (kWh) </Text>
//           </div>

//           <Popover
//             variant='icon-slim-bordered'
//             popoverPosition='bottom'
//             popoverUnstyled
//             popoverWidth={250}
//             className='absolute! inset-0'
//             buttonClassName={clsx('absolute top-4 right-4')}
//             buttonContent={() => <Text className='h-5 w-5 font-medium text-[10px]/5'> i </Text>}>
//             <Text variant='dim-2' className='rounded-c1 border border-accessory-1 px-6 py-3 text-2xs!'>
//               {dashboard('subscriptions.query-district-average-note')}
//             </Text>
//           </Popover>
//         </div>
//       </div>
//     </div>
//   )
// })
