import { use$ } from '@legendapp/state/react'
import { global$, notificationsApi } from '@mass/api'
import { ArchiveIcon, DotsVerticalIcon } from '@mass/icons'
import { type BlobType, i18n, useDate } from '@mass/utils'
import { type FC, memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Popover, Table, type TableColumnOptions, Text, ui$ } from '../../shared'
import { notifications$ } from '../stores/notifications'
import { subscription$ } from '../stores/subscription'

const useTableColumns = () => {
  const { t: common } = useTranslation('common')
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')
  const subscriptions = use$(() => subscription$.subscriptions.get()?.content ?? [])
  const isActive = use$(() => notifications$.query.values.status.get() === 'active')

  const format = useDate('DD MMMM YYYY HH:mm')

  return [
    {
      key: `title.${language.toUpperCase() as 'TR' | 'EN'}`,
      label: common('title'),

      render(value, { status }) {
        return (
          <div className='flex items-center gap-4'>
            {status === 'UNREAD' && <span className='h-3 w-3 rounded-max bg-primary' />}
            <Text variant={status === 'UNREAD' ? 'dim-semibold' : 'dim'}> {value} </Text>
          </div>
        )
      },
    },
    {
      key: 'subscriptionId',
      label: common('subscription'),

      render(value) {
        return <Text variant='dim'> {subscriptions.find(subscription => subscription.id === value)?.name ?? '-'} </Text>
      },
    },
    {
      key: `category.${language.toUpperCase() as 'TR' | 'EN'}`,
      label: common('notification-type'),

      render(value) {
        return <Text variant='dim'> {value} </Text>
      },
    },
    {
      key: 'createdAt',
      label: common('date'),

      render(value) {
        return <Text variant='dim'> {format(value)} </Text>
      },

      sortable: (rowA, rowB) => {
        const a = +new Date(rowA.original.createdAt)
        const b = +new Date(rowB.original.createdAt)

        return a - b
      },
    },
    ...(isActive
      ? [
          {
            key: 'id',
            label: common('actions'),

            render(_value, row) {
              return (
                <Popover
                  variant='icon-slim-bordered'
                  popoverPosition='bottom end'
                  className='flex justify-end'
                  buttonContent={() => <DotsVerticalIcon className='text-dim-3' />}>
                  <Button
                    variant='hover-warning'
                    className='justify-between!'
                    onClick={async e => {
                      e.stopPropagation()

                      if (row.status !== 'ARCHIVED') {
                        await notificationsApi.markAsRead({
                          query: {
                            id: row.id,
                          },

                          params: {
                            tag: 'ARCHIVED',
                          },
                        })

                        await notifications$.query.filter()
                      }
                    }}>
                    {common('archive')}

                    <ArchiveIcon className='h-8 w-8' />
                  </Button>
                </Popover>
              )
            },
          } satisfies TableColumnOptions<Dashboard.Notifications['notifications'][0]>,
        ]
      : []),
  ] satisfies TableColumnOptions<Dashboard.Notifications['notifications'][0]>[]
}

export const NotificationResults: FC = memo(() => {
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')
  const columns = useTableColumns()

  return (
    <Table
      data={notifications$.notifications}
      columns={columns}
      selectable={row => {
        if (row.status === 'UNREAD') {
          notificationsApi.markAsRead({
            query: {
              id: row.id,
            },
          })

          notifications$.notifications.set(
            notifications$.notifications.get().map(notification => {
              if (notification.id === row.id) {
                return {
                  ...notification,
                  status: 'READ',
                }
              }

              return notification as BlobType
            }),
          )
        }

        ui$.regular.set({
          title: row.title[language.toUpperCase() as 'TR' | 'EN'],
          description: row.textContent[language.toUpperCase() as 'TR' | 'EN'],
          onConfirm: (async () => void 0) as BlobType,
          onCancel: (async () => void 0) as BlobType,
          toast: false,
        })
        ui$.onChangeModal('regular', true)
      }}
    />
  )
})
