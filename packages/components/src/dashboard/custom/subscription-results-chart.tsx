/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { use$ } from '@legendapp/state/react'
import { type BlobType, useGranularityFormat, useNumberFormat } from '@mass/utils'
import clsx from 'clsx'
import { type FC, type ReactNode, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Bar,
  BarChart,
  Brush,
  CartesianGrid,
  Legend,
  Rectangle,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'
import { Text } from '../../shared'
import { subscription$ } from '../stores/subscription'

type LegendPayload = {
  type: 'square'
  value: string
  color: string
}

const colorGroups = [
  ['#7CD4FD', '#36BFFA', '#0BA5EC'],
  ['#FDE272', '#FAC515', '#EAAA08'],
  ['#A6EF67', '#85E13A', '#66C61C'],
  ['#EEAAFD', '#E478FA', '#D444F1'],
  ['#FEA3B4', '#FD6F8E', '#F63D68'],
]

const oppositeColorGroups = [
  ['#EF6820', '#EF6820', '#EF6820'], // opposites: #7CD4FD, #36BFFA, #0BA5EC
  ['#B692F6', '#B692F6', '#B692F6'], // opposites: #FDE272, #FAC515, #EAAA08
  ['#591098', '#591098', '#591098'], // opposites: #A6EF67, #85E13A, #66C61C
  ['#115502', '#115502', '#115502'], // opposites: #EEAAFD, #E478FA, #D444F1
  ['#015C4B', '#015C4B', '#015C4B'], // opposites: #FEA3B4, #FD6F8E, #F63D68
]

const BAR_RADIUS = [8, 8, 0, 0] as const

const CustomBar = (type: 't0' | 't1' | 't2' | 't3' | 'p0' | 'p1' | 'p2' | 'p3') => {
  return (props: { compare?: string[]; oppositeCompare?: string[]; [key: string]: BlobType }) => {
    const { compare, oppositeCompare } = props

    const i = type === 't1' || type === 'p1' ? 0 : type === 't2' || type === 'p2' ? 1 : 2

    if (type.startsWith('p')) {
      return (
        <Rectangle
          {...props}
          fill={oppositeCompare?.[i] || '#EF6820'}
          radius={type.endsWith('0') || type.endsWith('1') ? (BAR_RADIUS as BlobType) : 0}
        />
      )
    }

    return (
      <Rectangle
        {...props}
        fill={compare?.[i] || '#0BA5EC'}
        radius={type.endsWith('0') || type.endsWith('1') ? (BAR_RADIUS as BlobType) : 0}
      />
    )
  }
}

type CustomPayload = Dashboard.PopulatedUsages & {
  compare: string[]
  oppositeCompare: string[]
}

interface TooltipPayload {
  dataKey: string
  name: string
  value: number
  payload: CustomPayload
  hide: false
}

const CustomTooltip = ({
  active,
  payload,
  label,
}: {
  active?: boolean | undefined
  payload?: TooltipPayload[] | undefined
  label?: BlobType | undefined
}) => {
  const isVisible = active && payload && payload.length > 0
  const { t: common } = useTranslation('common')
  const populatedGranularity = use$(() => subscription$.query.populated.granularity.get())
  const period = use$(() => subscription$.query.values.period.get())
  const timeMode = use$(() => subscription$.usage.timeMode.get())
  const isProduction = use$(() => subscription$.selectedSubscription.get()?.type === 'electricity-production')

  const granularityFormat = useGranularityFormat(
    populatedGranularity === 'hour'
      ? period === 'monthly'
        ? 'month'
        : 'hour'
      : period === 'yearly'
        ? 'month'
        : populatedGranularity,
  )

  const List = useMemo(() => {
    // biome-ignore lint/correctness/useHookAtTopLevel: Redundant
    const numberFormat = useNumberFormat()
    const datas = payload ?? []

    const target = datas.at(0)?.payload
    const result: ReactNode[] = []

    if (timeMode === 'polychronic') {
      result.unshift(
        <div
          key='t3-bar'
          className={clsx(
            'flex flex-nowrap', // flex
            'items-center gap-2', // spacing
          )}>
          <span
            className={clsx('h-6 w-6 rounded-full')}
            style={{
              backgroundColor: target?.compare?.[2],
            }}
          />

          <Text variant='dim-xs'> {common('t3')}: </Text>
          <Text variant='dimmer-xs'> {numberFormat(target?.t3 ?? 0)} kWh </Text>
        </div>,
      )
      result.unshift(
        <div
          key='t2-bar'
          className={clsx(
            'flex flex-nowrap', // flex
            'items-center gap-2', // spacing
          )}>
          <span
            className={clsx('h-6 w-6 rounded-full')}
            style={{
              backgroundColor: target?.compare?.[1],
            }}
          />

          <Text variant='dim-xs'> {common('t2')}: </Text>
          <Text variant='dimmer-xs'> {numberFormat(target?.t2 ?? 0)} kWh </Text>
        </div>,
      )
      result.unshift(
        <div
          key='t1-bar'
          className={clsx(
            'flex flex-nowrap', // flex
            'items-center gap-2', // spacing
          )}>
          <span
            className={clsx('h-6 w-6 rounded-full')}
            style={{
              backgroundColor: target?.compare?.[0],
            }}
          />

          <Text variant='dim-xs'> {common('t1')}: </Text>
          <Text variant='dimmer-xs'> {numberFormat(target?.t1 ?? 0)} kWh </Text>
        </div>,
      )
    } else {
      result.unshift(
        <div
          key='consumption-bar'
          className={clsx(
            'flex flex-nowrap', // flex
            'items-center gap-2', // spacing
          )}>
          <span
            className={clsx('h-6 w-6 rounded-full')}
            style={{
              backgroundColor: target?.compare?.[0],
            }}
          />

          <Text variant='dim-xs'> {common('consumption')}: </Text>
          <Text variant='dimmer-xs'> {numberFormat(target?.t0 ?? 0)} kWh </Text>
        </div>,
      )
    }

    if (isProduction && target?.p0) {
      result.push(
        <div
          key='production-bar'
          className={clsx(
            'flex flex-nowrap', // flex
            'items-center gap-2', // spacing
          )}>
          <span
            className={clsx('h-6 w-6 rounded-full')}
            style={{
              backgroundColor: target?.oppositeCompare?.[0],
            }}
          />

          <Text variant='dim-xs'> {common('production')}: </Text>
          <Text variant='dimmer-xs'> {numberFormat(target?.p0 ?? 0)} kWh </Text>
        </div>,
      )
    }

    return result
  }, [payload, timeMode, isProduction, common])

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-4 p-4', // spacing
        'rounded-c1 border border-accessory-1',
        'bg-white',
        'w-max min-w-[200px] max-w-[300px]',
      )}
      style={{ visibility: isVisible ? 'visible' : 'hidden' }}>
      {isVisible && (
        <>
          <Text variant='dim-base'>{label instanceof Date ? granularityFormat(label) : common(label as string)}</Text>
          <div
            className={clsx(
              'flex flex-col', // flex
            )}>
            {List}
          </div>
        </>
      )}
    </div>
  )
}

export const SubscriptionResultsChart: FC = () => {
  const { t: common } = useTranslation('common')

  const isProduction = use$(() => subscription$.selectedSubscription.get()?.type === 'electricity-production')
  const timeMode = use$(() => subscription$.usage.timeMode.get())
  const populatedGranularity = use$(() => subscription$.query.populated.granularity.get())
  const period = use$(() => subscription$.query.values.period.get())

  const { populated, legends } = use$(() => {
    const results = subscription$.usage.populated.get()

    const populatedResults = results.map(rawResult => {
      const result = {
        ...rawResult,
      } as CustomPayload

      if (result.name instanceof Date) {
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.compare = colorGroups[0]!
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.oppositeCompare = oppositeColorGroups[0]!
      } else if (result.name === 'average') {
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.compare = colorGroups[1]!
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.oppositeCompare = oppositeColorGroups[1]!
      } else if (result.name === 'city-average') {
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.compare = colorGroups[2]!
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.oppositeCompare = oppositeColorGroups[2]!
      } else if (result.name === 'district-average') {
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.compare = colorGroups[3]!
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result.oppositeCompare = oppositeColorGroups[3]!
      }

      return result
    })

    const baseLegends = populatedResults
      .filter(result => typeof result.name === 'string')
      .reduce((acc, curr) => {
        acc.push({
          type: 'square',
          value: common(curr.name as string),
          // biome-ignore lint/style/noNonNullAssertion: Redundant
          color: curr.compare![0]!,
        })

        if (curr.name === 'average' && isProduction) {
          acc.push({
            type: 'square',
            value: common('average-production'),
            color: '#B692F6',
          })
        }

        return acc
      }, [] as LegendPayload[])

    return {
      populated: populatedResults,

      legends: [
        ...baseLegends,
        {
          type: 'square',
          value: common('consumption'),
          color: '#0BA5EC',
        },
        ...(isProduction
          ? [
              {
                type: 'square',
                value: common('production'),
                color: '#EF6820',
              },
            ]
          : []),
      ] as LegendPayload[],
    }
  })

  const granularityFormat = useGranularityFormat(
    populatedGranularity === 'hour'
      ? period === 'monthly'
        ? 'only-month'
        : 'only-hour'
      : period === 'yearly'
        ? 'only-month'
        : populatedGranularity,
  )

  const formatterX = (value: BlobType) => {
    return value instanceof Date ? granularityFormat(value) : ''
  }

  return (
    <div className='flex min-h-[600px] w-full'>
      <ResponsiveContainer width='100%' height='100%'>
        <BarChart
          data={populated}
          margin={{
            top: 20,
            right: 0,
            left: 0,
            bottom: 0,
          }}>
          <CartesianGrid vertical={false} strokeDasharray='3 3' />
          <XAxis dataKey='name' fontSize={12} tickFormatter={formatterX} stroke='#A4A7AE' />
          <YAxis unit=' kWh' fontSize={12} stroke='#A4A7AE' />
          <Legend verticalAlign='top' wrapperStyle={{ lineHeight: '40px' }} payload={legends} />
          <Tooltip content={CustomTooltip as BlobType} />

          <Bar dataKey='t0' maxBarSize={80} shape={CustomBar('t0')} hide={timeMode === 'polychronic'} />
          {[...new Array(3)]
            .map((_, i) => i)
            .reverse()
            .map(i => (
              <Bar
                key={`t${i + 1}`}
                dataKey={`t${i + 1}`}
                stackId='a'
                maxBarSize={80}
                shape={CustomBar(`t${i + 1}` as 't1' | 't2' | 't3')}
                hide={timeMode !== 'polychronic'}
              />
            ))}

          <Bar dataKey='p0' maxBarSize={80} shape={CustomBar('p0')} hide={!isProduction} />

          <Brush dataKey='name' height={30} stroke='#0d9fe7' travellerWidth={10} tickFormatter={() => ''} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
