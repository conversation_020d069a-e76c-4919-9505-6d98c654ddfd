import { use$ } from '@legendapp/state/react'
import { subscriptionsApi } from '@mass/api'
import { FilterLinesIcon } from '@mass/icons'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  Button,
  Combo,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Select,
  Text,
  ui$,
} from '../../../shared'
import { subscription$ } from '../../stores/subscription'
import { subscriptionFilters$ } from '../../stores/subscription-filters'

export const SubscriptionFiltersModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')
  const regions = use$(() => subscription$.regions.get() ?? [])

  const subscriptionType = use$(() => subscriptionFilters$.get().eq('individual', { defaultValue: 'all' }))
  const facilityType = use$(() => subscriptionFilters$.get().eq('type', { defaultValue: 'all' }))
  const distributionCompany = use$(() => subscriptionFilters$.get().eq('regionId', { defaultValue: 'all' }))

  const handleClearFilters = async () => {
    try {
      subscriptionFilters$.clearFilters()

      const subscriptions = await subscriptionsApi.list({
        params: subscriptionFilters$.params.get(),
        invalidateCache: true,
      })

      subscription$.subscriptions.set(subscriptions)

      ui$.onChangeModal('dashboard.subscription-filters', false)
      toast.success(common('filters-cleared'))
    } catch (err) {
      console.log(err)
      toast.error(common('something-went-wrong'))
    }
  }

  const handleFilter = async () => {
    try {
      console.log(subscriptionFilters$.params.get())

      const subscriptions = await subscriptionsApi.list({
        params: subscriptionFilters$.params.get(),
        invalidateCache: true,
      })

      subscription$.subscriptions.set(subscriptions)

      ui$.onChangeModal('dashboard.subscription-filters', false)

      toast.success(common('filters-applied'))
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.subscription-filters'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[400px]! sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<FilterLinesIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.filters')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleClearFilters}>
                {common('clear-filters')}
              </Button>
              <Button variant='primary' onClick={handleFilter}>
                {common('filter')}
              </Button>
            </DialogFooter>
          }>
          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription-type')} </Text>

            <Select
              value={subscriptionType.value.toString()}
              options={[
                { value: 'all', label: common('all') },
                { value: 'true', label: common('neutral-person') },
                { value: 'false', label: common('artificial-person') },
              ]}
              onValueChange={value =>
                value === 'all' ? subscriptionType.set('all') : subscriptionType.set(value === 'true')
              }
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('facility-type')} </Text>

            <Select
              value={facilityType.value}
              options={[
                { value: 'all', label: common('all') },
                { value: 'electricity-production', label: common('production') },
                { value: 'electricity-consumption', label: common('consumption') },
              ]}
              onValueChange={facilityType.set}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('distribution-company')} </Text>

            <Combo
              value={distributionCompany.value}
              options={[
                { value: 'all', label: common('all') },
                ...regions.map(region => ({ value: region.id, label: region.name })),
              ]}
              onValueChange={distributionCompany.set}
              className='max-w-[225px]'
              customSort={(_query, a, b) => {
                return a.label.includes(' EDAŞ') ? 1 : b.label.includes(' EDAŞ') ? -1 : 0
              }}
            />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
