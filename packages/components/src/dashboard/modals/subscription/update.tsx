import { use$ } from '@legendapp/state/react'
import { subscriptionsApi, updateSubscriptionPayload } from '@mass/api'
import { EditIcon } from '@mass/icons'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Input,
  Text,
  ui$,
} from '../../../shared'
import { subscription$ } from '../../stores/subscription'
import { updateSubscription$ } from '../../stores/update-subscription'

export const UpdateSubscriptionModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const name = use$(() => updateSubscription$.get().use('name', updateSubscriptionPayload.get('name')))

  const handleCancel = () => {
    updateSubscription$.clearPayload()
    updateSubscription$.original.set(null)
    ui$.onChangeModal('dashboard.update-subscription', false)

    toast.success(common('operation-canceled'))
  }

  const handleUpdate = async () => {
    try {
      name.setDirty()

      if (Object.entries(updateSubscription$.errors.get()).some(([, value]) => value)) {
        return toast.error(common('fill-all-required-fields'))
      }

      const original = updateSubscription$.original.get()

      if (!original) {
        return toast.error(common('something-went-wrong'))
      }

      await subscriptionsApi.update({
        query: {
          id: original.id,
        },
        payload: updateSubscription$.payload.get() as Api.ExtractPayload<Api.Services['subscriptions']['update']>,
      })

      subscription$.subscriptions.set(
        await subscriptionsApi.list({
          invalidateCache: true,
        }),
      )

      toast.success(common('operation-completed'))
      ui$.onChangeModal('dashboard.update-subscription', false)
      updateSubscription$.clearPayload()
      updateSubscription$.original.set(null)
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.update-subscription'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[275px]! sm:max-w-[500px]'
          header={
            <DialogHeader
              icon={<EditIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.update')}
              description={dashboard('subscriptions.update-description')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleCancel}>
                {common('cancel')}
              </Button>
              <Button variant='primary' onClick={handleUpdate}>
                {common('update')}
              </Button>
            </DialogFooter>
          }>
          <div className='flex items-center justify-between'>
            <Text variant='subtitle'> {common('subscription-name')} </Text>

            <Input
              placeholder={dashboard('subscriptions.example.name')}
              value={name.value ?? ''}
              error={name.error}
              onChange={e => name.set(e.target.value)}
              className='max-w-[225px]'
            />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
