import { Memo, use$, useObservable } from '@legendapp/state/react'
import { global$, subscriptionsApi } from '@mass/api'
import { DownloadIcon } from '@mass/icons'
import { i18n } from '@mass/utils'
import { type FC, memo } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  Button,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Select,
  Text,
  ui$,
} from '../../../shared'
import { MAPPED_PERIODS, subscription$ } from '../../stores/subscription'

export const SubscriptionQueryExportModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const fileType = useObservable<'pdf' | 'csv' | 'xlsx'>('pdf')
  const exportHourly = useObservable(false)

  const isHourly = use$(() => subscription$.query.populated.granularity.get() === 'hour')
  const isLoading = use$(() => subscription$.usage.isLoading.get())

  const handleCancel = () => {
    fileType.set('pdf')
    exportHourly.set(false)

    ui$.onChangeModal('dashboard.export-subscription-query', false)
  }

  const handleExport = async () => {
    try {
      subscription$.usage.isLoading.set(true)

      await toast.promise(
        async () => {
          const populated = subscription$.query.populated.get()

          if (!populated) {
            throw new Error('something-went-wrong')
          }

          const period = subscription$.query.values.period.peek()

          const { files } = await subscriptionsApi.usage.export({
            query: {
              // biome-ignore lint/style/noNonNullAssertion: Redundant
              id: subscription$.selectedSubscription.get()!.id,
            },

            params: {
              ...populated,
              granularity: exportHourly.get() ? 'hour' : period === 'yearly' ? 'month' : MAPPED_PERIODS[period],
              fileType: fileType.get(),
              language: global$.language.get() ?? (i18n.language as 'tr' | 'en'),
            },
          })

          for (const file of files) {
            const link = document.createElement('a')
            link.href = URL.createObjectURL(await (await fetch(file.url)).blob())
            link.download = file.fileName
            link.setAttribute('target', '_blank')
            link.click()
            link.remove()
          }

          fileType.set('pdf')
          exportHourly.set(false)
        },
        {
          loading: common('wait'),
          success: common('exported'),
          error: common('something-went-wrong'),
        },
      )

      ui$.onChangeModal('dashboard.export-subscription-query', false)
    } catch (err) {
      toast.error(common('something-went-wrong'))
    } finally {
      subscription$.usage.isLoading.set(false)
    }
  }

  return (
    <DialogRoot name='dashboard.export-subscription-query'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[400px]! sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<DownloadIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.export-title')}
              description={dashboard('subscriptions.export-description')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4' disabled={isLoading}>
              <Button variant='bordered' onClick={handleCancel}>
                {common('cancel')}
              </Button>
              <Button variant='primary' onClick={handleExport} disabled={isLoading}>
                {common('export')}
              </Button>
            </DialogFooter>
          }>
          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('file-type')} </Text>

            <Memo>
              {() => (
                <Select
                  value={fileType.get()}
                  options={[
                    { value: 'pdf', label: 'PDF' },
                    { value: 'csv', label: 'CSV' },
                    { value: 'xlsx', label: 'XLSX' },
                  ]}
                  onValueChange={value => fileType.set(value)}
                  className='max-w-[225px]'
                  disabled={isLoading}
                />
              )}
            </Memo>
          </div>

          {isHourly && (
            <div className='mb-8 flex items-center justify-between gap-4'>
              <Text variant='subtitle'> {common('hourly-distribution')} </Text>

              <Memo>
                {() => (
                  <Select
                    value={`${exportHourly.get()}`}
                    options={[
                      { value: 'true', label: common('active') },
                      { value: 'false', label: common('passive') },
                    ]}
                    onValueChange={value => exportHourly.set(value === 'true')}
                    className='max-w-[225px]'
                    disabled={isLoading}
                  />
                )}
              </Memo>
            </div>
          )}
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
