/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { use$ } from '@legendapp/state/react'
import { global$, newSubscriptionPayload, subscriptionsApi } from '@mass/api'
import { PlusIcon } from '@mass/icons'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import {
  Button,
  Combo,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Input,
  Select,
  Text,
  ui$,
} from '../../../shared'
import { newSubscription$ } from '../../stores/new-subscription'
import { subscription$ } from '../../stores/subscription'

export const NewSubscriptionModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')
  const regions = use$(() => subscription$.regions.get() ?? [])

  const name = use$(() => newSubscription$.get().use('name', newSubscriptionPayload.get('name')))
  const individual = use$(() => newSubscription$.get().use('individual', newSubscriptionPayload.get('individual')))
  const personIdentifier = use$(() => {
    const individualValue = newSubscription$.get().use('individual', newSubscriptionPayload.get('individual'))

    if (individualValue.value === false) {
      return newSubscription$.get().use('personIdentifier', newSubscriptionPayload.get('personIdentifier'))
    }

    return newSubscription$.get().use('personIdentifier')
  })
  const regionId = use$(() => newSubscription$.get().use('regionId', newSubscriptionPayload.get('regionId')))
  const installationId = use$(() =>
    newSubscription$.get().use('installationId', newSubscriptionPayload.get('installationId')),
  )

  const handleCancel = () => {
    newSubscription$.clearPayload()
    ui$.onChangeModal('dashboard.new-subscription', false)

    toast.success(common('operation-canceled'))
  }

  const handleCreate = async () => {
    try {
      name.setDirty()
      individual.setDirty()
      personIdentifier.setDirty()
      regionId.setDirty()
      installationId.setDirty()

      if (Object.entries(newSubscription$.errors.get()).some(([, value]) => value)) {
        return toast.error(common('fill-all-required-fields'))
      }

      if (individual.value === true) {
        const user = global$.user.get()

        await subscriptionsApi.create({
          payload: {
            ...(newSubscription$.payload.get() as Api.ExtractPayload<Api.Services['subscriptions']['create']>),
            personIdentifier: individual.value === true && user?.type === 'end' ? user.tckn : '',
          },
        })
      } else {
        await subscriptionsApi.create({
          payload: newSubscription$.payload.get() as Api.ExtractPayload<Api.Services['subscriptions']['create']>,
        })
      }

      subscription$.subscriptions.set(
        await subscriptionsApi.list({
          invalidateCache: true,
        }),
      )

      toast.success(common('operation-completed'))
      ui$.onChangeModal('dashboard.new-subscription', false)
      newSubscription$.clearPayload()
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.new-subscription'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[600px]! sm:max-w-[500px]'
          header={
            <DialogHeader
              icon={<PlusIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.new')}
              description={dashboard('subscriptions.new-description')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleCancel}>
                {common('cancel')}
              </Button>
              <Button variant='primary' onClick={handleCreate}>
                {common('add')}
              </Button>
            </DialogFooter>
          }>
          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('distribution-company')} </Text>

            <Combo
              value={regionId.value}
              error={regionId.error}
              options={regions.map(region => ({ value: region.id, label: region.name }))}
              onValueChange={regionId.set}
              className='max-w-[225px]'
              customSort={(_query, a, b) => {
                return a.label.includes(' EDAŞ') ? 1 : b.label.includes(' EDAŞ') ? -1 : 0
              }}
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('installation-id')} </Text>

            <Input
              placeholder={dashboard('subscriptions.example.installation-id')}
              value={installationId.value ?? ''}
              error={installationId.error}
              onChange={e => installationId.set(e.target.value)}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription-type')} </Text>

            <Select
              placeholder={common('select-an-option')}
              value={individual.value?.toString() ?? ''}
              options={[
                { value: 'true', label: common('neutral-person') },
                { value: 'false', label: common('artificial-person') },
              ]}
              error={individual.error}
              onValueChange={value => individual.set(value === 'true')}
              className='max-w-[225px]'
            />
          </div>

          {individual.value === false && (
            <div className='mb-8 flex items-center justify-between gap-4'>
              <Text variant='subtitle'> {common('person-identifier')} </Text>

              <Input
                placeholder={dashboard('subscriptions.example.personIdentifier')}
                value={personIdentifier.value ?? ''}
                error={personIdentifier.error}
                onChange={e => personIdentifier.set(e.target.value)}
                className='max-w-[225px]'
              />
            </div>
          )}

          <div className='flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription-name')} </Text>

            <Input
              placeholder={dashboard('subscriptions.example.name')}
              value={name.value ?? ''}
              error={name.error}
              onChange={e => name.set(e.target.value)}
              className='max-w-[225px]'
            />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
