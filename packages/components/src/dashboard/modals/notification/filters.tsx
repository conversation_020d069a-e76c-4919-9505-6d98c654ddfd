import { use$ } from '@legendapp/state/react'
import { global$, notificationsApi } from '@mass/api'
import { FilterLinesIcon } from '@mass/icons'
import { i18n } from '@mass/utils'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  Button,
  Combo,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Text,
  ui$,
} from '../../../shared'
import { notifications$ } from '../../stores/notifications'
import { subscription$ } from '../../stores/subscription'

export const NotificationFiltersModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const query = use$(() => notifications$.query.values.get())
  const subscriptions = use$(() => subscription$.subscriptions.get()?.content ?? [])
  const categories = use$(() => notifications$.categories.get())
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')

  const handleClearFilters = async () => {
    try {
      notifications$.query.clear()

      const { content: notifications } = await notificationsApi.list({
        params: {
          'filter:eq': [
            {
              status: 'UNREAD',
            },
          ],
          pageSize: '100',
        },
        invalidateCache: true,
      })
      notifications$.notifications.set(notifications)

      ui$.onChangeModal('dashboard.notifications-filters', false)
      toast.success(common('filters-cleared'))
    } catch (err) {
      console.log(err)
      toast.error(common('something-went-wrong'))
    }
  }

  const handleFilter = async () => {
    try {
      await notifications$.query.filter()

      toast.success(common('filters-applied'))
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.notifications-filters'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[400px]! sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<FilterLinesIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('notifications.filters')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleClearFilters}>
                {common('clear-filters')}
              </Button>
              <Button variant='primary' onClick={handleFilter}>
                {common('filter')}
              </Button>
            </DialogFooter>
          }>
          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription')} </Text>

            <Combo
              value={query.subscriptionId}
              options={[
                { value: 'all', label: common('all') },
                ...subscriptions.map(subscription => ({ value: subscription.id, label: subscription.name })),
              ]}
              onValueChange={value => notifications$.query.setSubscriptionId(value)}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('notification-type')} </Text>

            <Combo
              value={query.notificationType}
              options={[
                { value: 'all', label: common('all') },
                ...categories.map(category => ({
                  value: `${category.type}-${category.subtype}`,
                  label: category.label[language.toLowerCase() as 'tr' | 'en'],
                })),
              ]}
              onValueChange={value => notifications$.query.setNotificationType(value)}
              className='max-w-[225px]'
            />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
