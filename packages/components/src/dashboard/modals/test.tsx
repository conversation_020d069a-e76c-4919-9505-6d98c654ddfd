import { type FC, memo } from 'react'
import { Dialog<PERSON>ontent, DialogHeader, DialogOverlay, DialogPortal, DialogRoot } from '../../shared'

export const TestModal: FC = memo(() => {
  return (
    <DialogRoot name='dashboard.test'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent className='sm:max-h-[200px] sm:max-w-[200px]'>
          <DialogHeader title='Test' description='lorem ipsum dolor' />
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
