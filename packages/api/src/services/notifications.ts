import { type } from 'arktype'
import { apiBuilder } from '../utils/api-builder'

export const NOTIFICATION_CATEGORY_BASE = type({
  epias: '"true" | "false"',
  deleted: '"true" | "false"',
  fixed: '"true" | "false"',
  edas: '"true" | "false"',
  label: {
    // biome-ignore lint/style/useNamingConvention: Redundant
    EN: 'string',
    // biome-ignore lint/style/useNamingConvention: Redundant
    TR: 'string',
  },
  subcategories: type({
    '*': type({
      epias: '"true" | "false"',
      deleted: '"true" | "false"',
      fixed: '"true" | "false"',
      edas: '"true" | "false"',
      label: {
        // biome-ignore lint/style/useNamingConvention: Redundant
        EN: 'string',
        // biome-ignore lint/style/useNamingConvention: Redundant
        TR: 'string',
      },
    }),
  }).optional(),
})

export const NOTIFICATION_BASE = type({
  id: 'string',
  type: 'string',
  subtype: type('string').optional(),
  category: {
    // biome-ignore lint/style/useNamingConvention: Redundant
    TR: 'string',
    // biome-ignore lint/style/useNamingConvention: Redundant
    EN: 'string',
  },
  createdAt: 'string.date',
  title: {
    // biome-ignore lint/style/useNamingConvention: Redundant
    TR: 'string',
    // biome-ignore lint/style/useNamingConvention: Redundant
    EN: 'string',
  },
  textContent: {
    // biome-ignore lint/style/useNamingConvention: Redundant
    TR: 'string',
    // biome-ignore lint/style/useNamingConvention: Redundant
    EN: 'string',
  },
  status: "'UNREAD' | 'READ' | 'ARCHIVED'",
  statusChangedAt: 'string.date | null',
  regionId: 'string',
  subscriptionId: 'string',
})

export const notificationsApi = {
  categories: apiBuilder({
    url: '/setting/global/notifications.categories',
    cache: 'until-reload',

    response: type({
      value: {
        '*': NOTIFICATION_CATEGORY_BASE,
        notification: NOTIFICATION_CATEGORY_BASE,
        warning: NOTIFICATION_CATEGORY_BASE,
      },
    }),
  }),

  list: apiBuilder({
    url: '/notification',
    cache: 'validate',
    params: type({
      'filter:eq?': type({
        'status?': "'UNREAD' | 'READ' | 'ARCHIVED'",
        'subscriptionId?': 'string',
        'type?': 'string',
        'subtype?': type('string'),
      }).array(),

      'pageNumber?': 'string.alphanumeric',
      'pageSize?': 'string.alphanumeric',
      'orderBy?': type("'createdAt:desc' | 'createdAt:asc'"),
    }),

    encodeParamsKeysUrl: ['filter:eq'],

    response: type({
      content: NOTIFICATION_BASE.array(),
      pageable: {
        pageNumber: 'number',
        pageSize: 'number',
        sort: {
          sorted: 'boolean',
          empty: 'boolean',
          unsorted: 'boolean',
        },
        offset: 'number',
        paged: 'boolean',
        unpaged: 'boolean',
      },
      last: 'boolean',
      totalPages: 'number',
      totalElements: 'number',
      size: 'number',
      number: 'number',
      sort: {
        sorted: 'boolean',
        empty: 'boolean',
        unsorted: 'boolean',
      },
      first: 'boolean',
      numberOfElements: 'number',
      empty: 'boolean',
    }),
  }),

  markAsRead: apiBuilder({
    method: 'PATCH',
    url: '/notification/$id/read',

    query: type({
      id: 'string',
    }),

    params: type({
      tag: "'ARCHIVED'",
    }),

    response: type('unknown'),
  }),

  count: apiBuilder({
    url: '/notification/count',
    cache: 'validate',

    params: type({
      'read?': 'boolean',
      'archived?': 'boolean',
    }),

    response: type({
      count: 'number',
    }),
  }),
} as const
