{"devDependencies": {"@mass/utils": "workspace:*", "@legendapp/state": "^3.0.0-beta.31", "@ozaco/cli": "^0.0.13", "typescript": "^5.8.3", "arktype": "^2.1.20"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}}, "files": ["dist"], "name": "@mass/api", "peerDependencies": {"@mass/utils": "workspace:*", "@legendapp/state": ">= 3.0.0-beta.31", "typescript": ">= 5.8.3", "arktype": ">= 2.1.20"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}