import type { FC, SVGProps } from 'react'

export const CheckIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Check </title>

    <path d='M20 6L9 17L4 12' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' />
  </svg>
)
