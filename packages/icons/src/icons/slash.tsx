import type { FC, SVGProps } from 'react'

export const SlashIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Slash </title>

    <path d='M7 22L17 2' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' />
  </svg>
)
