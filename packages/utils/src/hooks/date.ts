import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/tr'
import 'dayjs/locale/en'

// biome-ignore lint/style/noExportedImports: Redundant
import dayjs from 'dayjs'

dayjs.extend(utc)

import { useMemo } from 'react'
import { i18n } from '../i18n'

export const utcDate = (value?: string | null | Date) => dayjs(value?.toString()).utcOffset(0)

export const useDate = (dateFormat: string) => {
  return (value: string | Date) => utcDate(value.toString()).locale(i18n.language).format(dateFormat)
}

export const useGranularityFormat = (granularity?: 'year' | 'hour' | 'day' | 'month' | 'only-hour' | 'only-month') => {
  return useMemo(() => {
    // biome-ignore lint/correctness/useHookAtTopLevel: Redundant
    return useDate(
      granularity === 'year'
        ? 'YYYY'
        : granularity === 'month'
          ? 'MMMM YYYY'
          : granularity === 'day'
            ? 'DD MMMM YYYY'
            : granularity === 'only-hour'
              ? 'HH:mm'
              : granularity === 'only-month'
                ? 'MM/YY'
                : 'HH:mm DD MMMM YYYY',
    )
  }, [granularity])
}

export const useNumberFormat = () => {
  const { format } = new Intl.NumberFormat(i18n.language === 'tr' ? 'es-ES' : 'en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

  return format
}

export { dayjs }
