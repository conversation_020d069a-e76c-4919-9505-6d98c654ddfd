import { describe, expect, test } from 'bun:test'

describe('utils', () => {
  test('exports', async () => {
    const keys = [
      'dayjs',
      'detector',
      'generateCacheKey',
      'i18n',
      'isEdge',
      'isPromise',
      'throttle',
      'useDate',
      'useScroll',
      'utcDate',
    ]
    const imported = await import('../src')
    const exported = Object.keys(imported)

    expect(exported).toEqual(keys)
  })
})
