apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-BRANCH_ID_PLACEHOLDER
  namespace: microservices
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontend-BRANCH_ID_PLACEHOLDER
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: frontend-BRANCH_ID_PLACEHOLDER
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - frontend-BRANCH_ID_PLACEHOLDER
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
        - name: frontend-BRANCH_ID_PLACEHOLDER-container
          image: FRONTEND_IMAGE_PLACEHOLDER
          ports:
            - containerPort: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontadm-<PERSON><PERSON><PERSON>_ID_PLACEHOLDER
  namespace: microservices
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontadm-BRANCH_ID_PLACEHOLDER
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: frontadm-BRANCH_ID_PLACEHOLDER
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - frontadm-BRANCH_ID_PLACEHOLDER
                topologyKey: kubernetes.io/hostname
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
        - name: frontadm-BRANCH_ID_PLACEHOLDER-container
          image: ADMIN_IMAGE_PLACEHOLDER
          ports:
            - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-BRANCH_ID_PLACEHOLDER-service
  namespace: microservices
spec:
  selector:
    app: frontend-BRANCH_ID_PLACEHOLDER
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: frontadm-BRANCH_ID_PLACEHOLDER-service
  namespace: microservices
spec:
  selector:
    app: frontadm-BRANCH_ID_PLACEHOLDER
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-BRANCH_ID_PLACEHOLDER-hpa
  namespace: microservices
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-BRANCH_ID_PLACEHOLDER
  minReplicas: 3
  maxReplicas: 16
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontadm-BRANCH_ID_PLACEHOLDER-hpa
  namespace: microservices
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontadm-BRANCH_ID_PLACEHOLDER
  minReplicas: 3
  maxReplicas: 16
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 50
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontend-BRANCH_ID_PLACEHOLDER-pdb
  namespace: microservices
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: frontend-BRANCH_ID_PLACEHOLDER
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontadm-BRANCH_ID_PLACEHOLDER-pdb
  namespace: microservices
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: frontadm-BRANCH_ID_PLACEHOLDER
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-BRANCH_ID_PLACEHOLDER-ingress
  namespace: microservices
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - BRANCH_ID_PLACEHOLDER.dashboard.massportal.com.tr
      secretName: frontend-BRANCH_ID_PLACEHOLDER-ingress-tls-2
  rules:
    - host: BRANCH_ID_PLACEHOLDER.dashboard.massportal.com.tr
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontend-BRANCH_ID_PLACEHOLDER-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontadm-BRANCH_ID_PLACEHOLDER-ingress
  namespace: microservices
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - BRANCH_ID_PLACEHOLDER.admin.massportal.com.tr
      secretName: frontadm-BRANCH_ID_PLACEHOLDER-ingress-tls-2
  rules:
    - host: BRANCH_ID_PLACEHOLDER.admin.massportal.com.tr
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontadm-BRANCH_ID_PLACEHOLDER-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-ingress
  namespace: microservices
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - dashboard.massportal.com.tr
        - admin.massportal.com.tr
        - test.massportal.com.tr
      secretName: frontend-ingress-full-3-tls
  rules:
    - host: admin.massportal.com.tr
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontadm-DEF_BRANCH-service
                port:
                  number: 8080
    - host: dashboard.massportal.com.tr
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontend-DEF_BRANCH-service
                port:
                  number: 8080
    - host: test.massportal.com.tr
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontend-DEF_BRANCH-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-ingress-test
  namespace: microservices
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - dashboard.massplatformtest.com
      secretName: frontend-ingress-full-3-test-tls
  rules:
    - host: dashboard.massplatformtest.com
      http:
        paths:
          - path: /api/?(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: request-handler-service
                port:
                  number: 5986
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: frontend-DEF_BRANCH-service
                port:
                  number: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: request-handler-internal-lb
  namespace: microservices
  annotations:
    metallb.universe.tf/address-pool: local-ip-pool
spec:
  type: LoadBalancer
  selector:
    app: request-handler
  ports:
    - protocol: TCP
      port: 5986
      targetPort: 5986
---
apiVersion: v1
kind: Service
metadata:
  name: ingress-nginx-internal-ipsec
  namespace: ingress-nginx
  annotations:
    metallb.universe.tf/address-pool: local-ip-pool
spec:
  type: LoadBalancer
  selector:
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/component: controller
  ports:
    - name: http
      port: 80
      targetPort: http
    - name: https
      port: 443
      targetPort: https
