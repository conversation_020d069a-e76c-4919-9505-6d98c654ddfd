server {
	listen 80 default_server;

	gzip on;
	gzip_min_length 1000;
	gzip_types text/plain text/xml application/javascript text/css;

	root /app;

	# normal routes
	# serve given url and default to index.html if not found
	# e.g. /, /user and /foo/bar will return index.html
	location / {
		add_header Cache-Control "no-store";
        add_header X-Frame-Options "DENY";
		try_files $uri $uri/index.html /index.html;
	}

	# files
	# static files (js, css, images, etc.)
	# for all routes matching static file extensions, serve files directly
	location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt|xml|json)$ {
		add_header Cache-Control "public, max-age=2678400";
        add_header X-Frame-Options "DENY";
		try_files $uri =404;
	}
}